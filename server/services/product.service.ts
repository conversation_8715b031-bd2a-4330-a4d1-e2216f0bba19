import { prisma } from "../db";
import { errorResponse, successResponse } from "../utils/response.util";
import {
  CreateProductInput,
  UpdateProductInput,
  ProductQueryInput,
  CategoryInput,
  ItemTypeInput,
  BidInput,
  CategoryQueryInput,
} from "../schemas/product.schema";

import autoBidService from "./autoBid.service";
import webSocketService from "./websocket.service";
import productPricingService from "./productPricing.service";
import notificationService from "./notification.service";
import { getJakartaTime } from "../utils/timezone.util";

class ProductService {
  // Helper method to convert datetime string to proper ISO format for Prisma
  private ensureSecondsInDateTime(dateTimeStr: string): Date {
    // If the string is missing seconds (format: YYYY-MM-DDTHH:mm), add :00
    let fullDateTime = dateTimeStr;
    if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/.test(dateTimeStr)) {
      fullDateTime = `${dateTimeStr}:00`;
    }

    // Convert to Date object which Prisma can handle properly
    const date = new Date(fullDateTime);

    // Validate the date
    if (isNaN(date.getTime())) {
      throw new Error(`Invalid datetime format: ${dateTimeStr}`);
    }

    return date;
  }

  // Helper method to generate slug from item name
  private generateSlug(itemName: string): string {
    return itemName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
      .trim()
      .substring(0, 100); // Limit length
  }

  // Helper method to ensure unique slug
  private async ensureUniqueSlug(
    baseSlug: string,
    excludeId?: string
  ): Promise<string> {
    let slug = baseSlug;
    let counter = 1;

    while (true) {
      const existing = await prisma.product.findUnique({
        where: { slug },
        select: { id: true },
      });

      if (!existing || (excludeId && existing.id === excludeId)) {
        return slug;
      }

      slug = `${baseSlug}-${counter}`;
      counter++;
    }
  }
  async createProduct(data: CreateProductInput, sellerId: string) {
    try {
      const { images, ...productData } = data;

      const slugBase = productData.itemName
        .toLowerCase()
        .trim()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-");

      const uniqueSuffix = Math.random().toString(36).substring(2, 8);

      let slug = `${slugBase}-${uniqueSuffix}`;
      if (!slug) {
        const baseSlug = this.generateSlug(productData.itemName);
        slug = await this.ensureUniqueSlug(baseSlug);
      } else {
        slug = await this.ensureUniqueSlug(slug);
      }

      // Transform datetime strings to Date objects for Prisma
      const transformedData = {
        ...productData,
        slug,
        auctionStartDate: productData.auctionStartDate
          ? this.ensureSecondsInDateTime(productData.auctionStartDate)
          : undefined,
        auctionEndDate: productData.auctionEndDate
          ? this.ensureSecondsInDateTime(productData.auctionEndDate)
          : undefined,
      };

      // Create product with images in a transaction
      const product = await prisma.$transaction(async (tx) => {
        // Create the product
        const newProduct = await tx.product.create({
          data: {
            ...transformedData,
            sellerId,
            status: "active",
          },
        });

        console.log("New product created:", images);

        // Create product images
        if (images && images.length > 0) {
          await tx.productImage.createMany({
            data: images.map((image, index) => ({
              productId: newProduct.id,
              imageUrl: image.imageUrl,
              altText: image.altText || `Product image ${index + 1}`,
              sortOrder: image.sortOrder || index,
              isMain: image.isMain,
            })),
          });
        }

        // Return product with images
        return await tx.product.findUnique({
          where: { id: newProduct.id },
          include: {
            images: {
              orderBy: { sortOrder: "asc" },
            },
            category: true,
            itemType: true,
            seller: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });
      });

      return successResponse("Product created successfully", product);
    } catch (error) {
      console.error("Create product error:", error);
      return errorResponse("Failed to create product");
    }
  }

  async getProducts(query: ProductQueryInput) {
    try {
      const {
        page,
        limit,
        sellType,
        categoryId,
        itemTypeId,
        status,
        search,
        sortBy,
        sortOrder,
        sellerId,
        minPrice,
        maxPrice,
      } = query;
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (sellType) where.sellType = sellType;
      if (categoryId) where.categoryId = categoryId;
      if (itemTypeId) where.itemTypeId = itemTypeId;
      if (status) where.status = status;
      if (sellerId) where.sellerId = sellerId; // Add sellerId filter
      if (minPrice !== undefined) where.priceUSD = { gte: minPrice };
      if (maxPrice !== undefined) where.priceUSD = { lte: maxPrice };
      if (search) {
        where.OR = [
          { itemName: { contains: search } },
          { description: { contains: search } },
          { slug: { contains: search } },
        ];
      }

      // Build order by clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            images: {
              orderBy: { sortOrder: "asc" },
            },
            category: true,
            itemType: true,
            seller: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            _count: {
              select: { bids: true },
            },
          },
        }),
        prisma.product.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return successResponse("Products retrieved successfully", {
        products,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      });
    } catch (error) {
      console.error("Get products error:", error);
      return errorResponse("Failed to retrieve products");
    }
  }

  async getProductById(id: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { id },
        include: {
          images: {
            orderBy: { sortOrder: "asc" },
          },
          category: true,
          itemType: true,
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          bids: {
            orderBy: { createdAt: "desc" },
            take: 10,
            include: {
              bidder: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          _count: {
            select: { bids: true },
          },
        },
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      return successResponse("Product retrieved successfully", product);
    } catch (error) {
      console.error("Get product error:", error);
      return errorResponse("Failed to retrieve product");
    }
  }

  async getProductBySlug(slug: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { slug },
        include: {
          images: {
            orderBy: { sortOrder: "asc" },
          },
          category: true,
          itemType: true,
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          bids: {
            orderBy: { createdAt: "desc" },
            take: 10,
            include: {
              bidder: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          _count: {
            select: { bids: true },
          },
        },
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      // Calculate auction status and time left
      const auctionData = this.calculateAuctionStatus(product);

      const productResponse = {
        ...product,
        auctionStatus: auctionData.status,
        timeLeft: auctionData.timeLeft,
      };

      return successResponse("Product retrieved successfully", productResponse);
    } catch (error) {
      console.error("Get product by slug error:", error);
      return errorResponse("Failed to retrieve product");
    }
  }

  /**
   * Handle extend bidding logic when a bid is placed
   */
  public async handleExtendBidding(product: any, bidAmount: number, bidderId?: string) {
    console.log(`🔍 handleExtendBidding called for product ${product.id}`);
    console.log(`📋 Product extend bidding settings check:`, {
      extendedBiddingEnabled: product.extendedBiddingEnabled,
      extendedBiddingMinutes: product.extendedBiddingMinutes,
      extendedBiddingDuration: product.extendedBiddingDuration,
      auctionEndDate: product.auctionEndDate,
      sellType: product.sellType
    });

    // Check if extend bidding is enabled for this product
    if (!product.extendedBiddingEnabled || !product.extendedBiddingMinutes || !product.extendedBiddingDuration) {
      console.log(`❌ Extend bidding not enabled for product ${product.id}:`, {
        extendedBiddingEnabled: product.extendedBiddingEnabled,
        extendedBiddingMinutes: product.extendedBiddingMinutes,
        extendedBiddingDuration: product.extendedBiddingDuration
      });
      return {
        extended: false,
        reason: 'Extended bidding not enabled'
      };
    }

    // Additional check for auction products
    if (product.sellType !== 'auction') {
      console.log(`❌ Product ${product.id} is not an auction product (sellType: ${product.sellType})`);
      return {
        extended: false,
        reason: 'Product is not an auction'
      };
    }

    // Use Jakarta time for consistency with auction scheduler
    const now = getJakartaTime();
    const auctionEndDate = new Date(product.auctionEndDate);

    // Calculate time remaining until auction ends (in minutes)
    const timeRemainingMs = auctionEndDate.getTime() - now.getTime();
    const timeRemainingMinutes = Math.floor(timeRemainingMs / (1000 * 60));

    console.log(`🔍 Extend bidding check for product ${product.id}:`);
    console.log(`  - Current time (Jakarta): ${now.toISOString()}`);
    console.log(`  - Auction end time: ${auctionEndDate.toISOString()}`);
    console.log(`  - Time remaining: ${timeRemainingMinutes} minutes`);
    console.log(`  - Trigger threshold: ${product.extendedBiddingMinutes} minutes`);
    console.log(`  - Extension duration: ${product.extendedBiddingDuration} minutes`);

    // Check if we're within the extend bidding trigger window
    if (timeRemainingMinutes <= product.extendedBiddingMinutes && timeRemainingMinutes > 0) {
      console.log(`🚀 Triggering auction extension for product ${product.id}`);

      // Extend the auction by the specified duration
      const newEndDate = new Date(auctionEndDate.getTime() + (product.extendedBiddingDuration * 60 * 1000));

      console.log(`📅 Extending auction from ${auctionEndDate.toISOString()} to ${newEndDate.toISOString()}`);

      // Use transaction to ensure atomicity
      const result = await prisma.$transaction(async (tx) => {
        // Update the product's auction end date with optimistic locking to prevent race conditions
        const updatedProduct = await tx.product.update({
          where: {
            id: product.id,
            // Only update if the auction end date hasn't changed (prevents double extensions)
            auctionEndDate: product.auctionEndDate
          },
          data: {
            auctionEndDate: newEndDate
          }
        });

        if (!updatedProduct) {
          throw new Error('Auction end date was already modified by another process');
        }

        // Create extension log
        const extensionLog = await tx.auctionExtensionLog.create({
          data: {
            productId: product.id,
            previousEndDate: auctionEndDate,
            newEndDate: newEndDate,
            extendedMinutes: product.extendedBiddingDuration,
            triggerBidAmount: bidAmount,
            triggeredBy: 'manual',
            triggeredBidderId: bidderId,
            extensionReason: 'bid_in_final_minutes'
          }
        });

        return { updatedProduct, extensionLog };
      });

      if (!result) {
        console.log(`⚠️ Auction extension skipped for product ${product.id} - auction end date was already modified`);
        return {
          extended: false,
          reason: 'Already extended by another process'
        };
      }

      // Send WebSocket notification about auction extension
      try {
        webSocketService.notifyAuctionExtended(product.id, {
          productId: product.id,
          newEndDate: newEndDate.toISOString(),
          extendedMinutes: product.extendedBiddingDuration,
          triggerBidAmount: bidAmount,
          triggeredAt: now.toISOString(),
          previousEndDate: auctionEndDate.toISOString(),
          extensionLogId: result.extensionLog.id
        });

        // Also send notification to all bidders
        await notificationService.sendAuctionExtensionNotification(product.id, {
          productId: product.id,
          newEndDate: newEndDate.toISOString(),
          extendedMinutes: product.extendedBiddingDuration,
          triggerBidAmount: bidAmount,
          triggeredAt: now.toISOString(),
          previousEndDate: auctionEndDate.toISOString(),
          extensionLogId: result.extensionLog.id
        });
      } catch (error) {
        console.error('❌ WebSocket/notification auction extension error:', error);
      }

      console.log(`✅ Auction extended by ${product.extendedBiddingDuration} minutes due to bid in final ${product.extendedBiddingMinutes} minutes`);
      console.log(`📝 Extension logged with ID: ${result.extensionLog.id}`);

      return {
        extended: true,
        newEndDate: newEndDate.toISOString(),
        previousEndDate: auctionEndDate.toISOString(),
        extendedMinutes: product.extendedBiddingDuration,
        extensionLogId: result.extensionLog.id
      };
    } else {
      console.log(`⏰ No extension needed: ${timeRemainingMinutes} minutes remaining (threshold: ${product.extendedBiddingMinutes})`);
      return {
        extended: false,
        timeRemaining: timeRemainingMinutes
      };
    }
  }

  /**
   * Calculate auction status and time left for a product
   */
  private calculateAuctionStatus(product: any): { status: string; timeLeft: string } {
    const now = new Date();
    const auctionStartDate = product.auctionStartDate;
    const auctionEndDate = product.auctionEndDate;

    if (product.sellType !== "auction") {
      return {
        status: "active",
        timeLeft: "Buy Now Available"
      };
    }

    // For auction products, check dates and status
    if (!auctionStartDate || !auctionEndDate) {
      // If no dates set, default to active but indicate missing dates
      return {
        status: "active",
        timeLeft: "Auction dates not set"
      };
    }

    const startDate = new Date(auctionStartDate);
    const endDate = new Date(auctionEndDate);

    if (now < startDate) {
      // Auction hasn't started yet
      const timeUntilStart = this.getTimeRemaining(startDate);
      return {
        status: "upcoming",
        timeLeft: `Starts in ${timeUntilStart}`
      };
    } else if (now > endDate) {
      // Auction has ended
      return {
        status: "ended",
        timeLeft: "Auction ended"
      };
    } else {
      // Auction is currently active
      const timeUntilEnd = this.getTimeRemaining(endDate);
      return {
        status: "active",
        timeLeft: `Ends in ${timeUntilEnd}`
      };
    }
  }

  /**
   * Get human-readable time remaining until a date
   */
  private getTimeRemaining(targetDate: Date): string {
    const now = new Date();
    const diff = targetDate.getTime() - now.getTime();

    if (diff <= 0) {
      return "Expired";
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  async updateProduct(id: string, data: UpdateProductInput, sellerId: string) {
    try {
      // Check if product exists and belongs to seller
      const existingProduct = await prisma.product.findFirst({
        where: { id, sellerId },
      });

      if (!existingProduct) {
        return errorResponse("Product not found or unauthorized");
      }

      const { images, ...productData } = data;

      // Handle slug update
      let slug = productData.slug;
      if (productData.itemName && !slug) {
        // Generate new slug from updated item name
        const baseSlug = this.generateSlug(productData.itemName);
        slug = await this.ensureUniqueSlug(baseSlug, id);
      } else if (slug) {
        // Ensure provided slug is unique
        slug = await this.ensureUniqueSlug(slug, id);
      }

      // Transform datetime strings to Date objects for Prisma
      const transformedData = {
        ...productData,
        ...(slug && { slug }),
        auctionStartDate: productData.auctionStartDate
          ? this.ensureSecondsInDateTime(productData.auctionStartDate)
          : undefined,
        auctionEndDate: productData.auctionEndDate
          ? this.ensureSecondsInDateTime(productData.auctionEndDate)
          : undefined,
      };

      const product = await prisma.$transaction(async (tx) => {
        // Update product
        await tx.product.update({
          where: { id },
          data: transformedData,
        });

        // Update images if provided
        if (images) {
          // Delete existing images
          await tx.productImage.deleteMany({
            where: { productId: id },
          });

          // Create new images
          if (images.length > 0) {
            await tx.productImage.createMany({
              data: images.map((image, index) => ({
                productId: id,
                imageUrl: image.imageUrl,
                altText: image.altText || `Product image ${index + 1}`,
                sortOrder: image.sortOrder || index,
                isMain: image.isMain || index === 0,
              })),
            });
          }
        }

        return await tx.product.findUnique({
          where: { id },
          include: {
            images: {
              orderBy: { sortOrder: "asc" },
            },
            category: true,
            itemType: true,
            seller: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });
      });

      return successResponse("Product updated successfully", product);
    } catch (error) {
      console.error("Update product error:", error);
      return errorResponse("Failed to update product");
    }
  }

  async deleteProduct(id: string, sellerId: string) {
    try {
      const product = await prisma.product.findFirst({
        where: { id, sellerId },
      });

      if (!product) {
        return errorResponse("Product not found or unauthorized");
      }

      await prisma.product.delete({
        where: { id },
      });

      return successResponse("Product deleted successfully");
    } catch (error) {
      console.error("Delete product error:", error);
      return errorResponse("Failed to delete product");
    }
  }

  async getCategories(query: CategoryQueryInput) {
    try {
      const categories = await prisma.category.findMany({
        where: {
          isActive: true,
          sellType: query.sellType ? query.sellType : undefined,
        },
        orderBy: { name: "asc" },
      });

      return successResponse("Categories retrieved successfully", categories);
    } catch (error) {
      console.error("Get categories error:", error);
      return errorResponse("Failed to retrieve categories");
    }
  }

  async createCategory(data: CategoryInput) {
    try {
      const category = await prisma.category.create({
        data,
      });

      return successResponse("Category created successfully", category);
    } catch (error) {
      console.error("Create category error:", error);
      return errorResponse("Failed to create category");
    }
  }

  async getItemTypes(categoryId?: string) {
    try {
      const where: any = { isActive: true };
      if (categoryId) where.categoryId = categoryId;

      const itemTypes = await prisma.itemType.findMany({
        where,
        orderBy: { name: "asc" },
        include: {
          category: true,
          _count: {
            select: { products: true },
          },
        },
      });

      return successResponse("Item types retrieved successfully", itemTypes);
    } catch (error) {
      console.error("Get item types error:", error);
      return errorResponse("Failed to retrieve item types");
    }
  }

  async createItemType(data: ItemTypeInput) {
    try {
      const itemType = await prisma.itemType.create({
        data,
      });

      return successResponse("Item type created successfully", itemType);
    } catch (error) {
      console.error("Create item type error:", error);
      return errorResponse("Failed to create item type");
    }
  }

  async placeBid(data: BidInput, bidderId: string) {
    // try {
    const { productId, bidAmount } = data;

    // Get product with current highest bid
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        bids: {
          orderBy: { amount: "desc" },
          take: 1,
        },
      },
    });

    if (!product) {
      return errorResponse("Product not found");
    }

    if (product.sellType !== "auction") {
      return errorResponse("This product is not an auction");
    }

    // Check auction timing and status
    const now = new Date();

    // For new auctions without start date, consider them started immediately
    const auctionStartDate = product.auctionStartDate ? new Date(product.auctionStartDate) : now;
    const auctionEndDate = product.auctionEndDate ? new Date(product.auctionEndDate) : null;

    // Check if auction hasn't started yet
    if (now < auctionStartDate) {
      return errorResponse("Auction has not started yet");
    }

    // Check if auction has ended
    if (auctionEndDate && now > auctionEndDate) {
      return errorResponse("Auction has ended");
    }

    // Check if product is active
    if (product.status !== "active") {
      return errorResponse("Product is not active");
    }

    // Check if bid is higher than current highest bid
    const currentHighestBid = product.bids[0]?.amount || product.priceUSD;
    const currentHighestBidNumber =
      typeof currentHighestBid === "number"
        ? currentHighestBid
        : Number(currentHighestBid);
    if (bidAmount <= currentHighestBidNumber) {
      return errorResponse(
        `Bid must be higher than current highest bid of $${currentHighestBidNumber}`
      );
    }

    // Create bid in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Mark all previous bids as not winning
      await tx.bid.updateMany({
        where: { productId },
        data: { isWinning: false },
      });

      // Create new bid
      const bid = await tx.bid.create({
        data: {
          productId,
          bidderId,
          amount: bidAmount,
          isWinning: true,
        },
        include: {
          bidder: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Update product with new current bid and bid count
      await tx.product.update({
        where: { id: productId },
        data: {
          currentBid: bidAmount,
          bidCount: { increment: 1 },
        },
      });

      return bid;
    });

    // Send WebSocket notification for new bid
    try {
      console.log('🔔 Preparing WebSocket notification for new bid...');

      // Get updated bid count
      const updatedProduct = await prisma.product.findUnique({
        where: { id: productId },
        select: { bidCount: true }
      });

      const bidData = {
        id: result.id,
        amount: Number(result.amount),
        currentBid: Number(result.amount),
        bidderId: result.bidderId,
        bidderName: result.bidder ? `${result.bidder.firstName} ${result.bidder.lastName}`.trim() : 'Anonymous',
        productId: result.productId,
        bidCount: updatedProduct?.bidCount || 0,
        createdAt: result.createdAt,
        bidType: 'manual',
        timestamp: new Date().toISOString()
      };

      console.log('📤 Sending WebSocket notification with data:', bidData);
      console.log('🎯 Broadcasting to channel:', `product:${productId}`);

      webSocketService.notifyNewBid(productId, bidData);

      console.log('✅ WebSocket notification sent successfully');
    } catch (error) {
      console.error('❌ WebSocket notification error:', error);
      // Don't fail the bid if WebSocket fails
    }

    // Check and handle extend bidding
    try {
      console.log(`🔍 Starting extend bidding check for product ${product.id}`);
      console.log(`📊 Product extend bidding settings:`, {
        extendedBiddingEnabled: product.extendedBiddingEnabled,
        extendedBiddingMinutes: product.extendedBiddingMinutes,
        extendedBiddingDuration: product.extendedBiddingDuration,
        auctionEndDate: product.auctionEndDate
      });

      const extensionResult = await this.handleExtendBidding(product, bidAmount, bidderId);
      console.log('🎯 Extension result:', extensionResult);

      if (extensionResult.extended) {
        console.log(`✅ AUCTION EXTENDED! New end date: ${extensionResult.newEndDate}`);
      } else {
        console.log(`❌ No extension: ${extensionResult.reason || 'Unknown reason'}`);
      }
    } catch (error) {
      console.error('❌ Extend bidding processing error:', error);
      // Don't fail the main bid if extend bidding fails
    }

    // Process auto-bids after successful manual bid
    try {
      await autoBidService.processAutoBids(productId, bidAmount, bidderId);
    } catch (error) {
      console.error('Auto-bid processing error:', error);
      // Don't fail the main bid if auto-bid processing fails
    }

    return successResponse("Bid placed successfully", result);
    // } catch (error) {
    //   console.error("Place bid error:", error);
    //   return errorResponse("Failed to place bid");
    // }
  }

  async getBidsByProductId(productId: string) {
    try {
      const bids = await prisma.bid.findMany({
        where: { productId },
        orderBy: { createdAt: "desc" },
        include: {
          bidder: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      return successResponse("Bids retrieved successfully", bids);
    } catch (error) {
      console.error("Get bids by product ID error:", error);
      return errorResponse("Failed to retrieve bids");
    }
  }

  async getUserBids(productId: string, userId: string) {
    try {
      const bids = await prisma.bid.findFirst({
        where: { bidderId: userId, product: { status: "active" }, productId },
        orderBy: [{ createdAt: "desc" }, { amount: "desc" }],
        include: {
          product: {
            select: {
              id: true,
              itemName: true,
              slug: true,
              currentBid: true,
              auctionEndDate: true,
            },
          },
        },
      });

      return successResponse("User bids retrieved successfully", bids);
    } catch (error) {
      console.error("Get user bids error:", error);
      return errorResponse("Failed to retrieve user bids");
    }
  }

  /**
   * Get products with converted prices for display
   */
  async getProductsWithConvertedPrices(targetCurrency: string = 'IDR', page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit;

      const products = await prisma.product.findMany({
        include: {
          images: {
            take: 3,
            orderBy: { sortOrder: 'asc' }
          },
          category: true,
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          bids: {
            orderBy: { amount: 'desc' },
            take: 1,
            include: {
              bidder: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      });

      const total = await prisma.product.count();

      // Convert prices for each product
      const productsWithConvertedPrices = await Promise.all(
        products.map(async (product) => {
          try {
            let convertedPrice = Number(product.priceUSD);
            let exchangeRate = 1;
            let priceSource = 'original';

            // Convert price if different currency (USD to target currency)
            if ('USD' !== targetCurrency) {
              const conversion = await productPricingService.convertProductPrice(
                Number(product.priceUSD),
                'USD',
                targetCurrency
              );
              convertedPrice = conversion.convertedPrice;
              exchangeRate = conversion.exchangeRate;
              priceSource = 'converted';
            }

            // Calculate auction status
            const now = new Date();
            let auctionStatus = 'not_auction';
            const isAuction = product.sellType === 'auction';

            if (isAuction) {
              if (product.auctionStartDate && now < product.auctionStartDate) {
                auctionStatus = 'upcoming';
              } else if (product.auctionEndDate && now > product.auctionEndDate) {
                auctionStatus = 'ended';
              } else {
                auctionStatus = 'active';
              }
            }

            // Get current highest bid
            const currentBid = product.bids.length > 0 ? Number(product.bids[0].amount) : null;
            let convertedCurrentBid = currentBid;

            if (currentBid && 'USD' !== targetCurrency) {
              try {
                const bidConversion = await productPricingService.convertProductPrice(
                  currentBid,
                  'USD',
                  targetCurrency
                );
                convertedCurrentBid = bidConversion.convertedPrice;
              } catch (error) {
                console.error(`Error converting bid for product ${product.id}:`, error);
                convertedCurrentBid = currentBid;
              }
            }

            return {
              ...product,
              // Original price info
              originalPrice: Number(product.priceUSD),
              originalCurrency: 'USD',

              // Converted price info
              price: convertedPrice, // Override with converted price
              convertedPrice,
              displayCurrency: targetCurrency,
              exchangeRate,
              priceSource,

              // Auction info
              auctionStatus,
              currentBid: convertedCurrentBid,
              originalCurrentBid: currentBid,

              // Bid count
              bidCount: product.bids.length,

              // Time remaining for auction
              timeRemaining: isAuction && product.auctionEndDate
                ? Math.max(0, product.auctionEndDate.getTime() - now.getTime())
                : null
            };
          } catch (error) {
            console.error(`Error processing product ${product.id}:`, error);
            // Return original product if conversion fails
            return {
              ...product,
              originalPrice: Number(product.priceUSD),
              originalCurrency: 'USD',
              convertedPrice: Number(product.priceUSD),
              displayCurrency: 'USD',
              exchangeRate: 1,
              priceSource: 'original',
              auctionStatus: product.sellType === 'auction' ? 'active' : 'not_auction',
              currentBid: product.bids.length > 0 ? Number(product.bids[0].amount) : null,
              bidCount: product.bids.length,
              timeRemaining: null
            };
          }
        })
      );

      return successResponse("Products with converted prices retrieved successfully", {
        products: productsWithConvertedPrices,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        targetCurrency,
        conversionInfo: {
          exchangeRateSource: 'database',
          lastUpdated: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Get products with converted prices error:', error);
      return errorResponse("Failed to get products with converted prices");
    }
  }

  /**
   * Get related products by category
   */
  async getRelatedProductsByCategory(categoryId: string, excludeProductId?: string, limit: number = 6) {
    try {
      const whereClause: any = {
        categoryId,
        status: 'active',
        isActive: true
      };

      if (excludeProductId) {
        whereClause.id = { not: excludeProductId };
      }

      const relatedProducts = await prisma.product.findMany({
        where: whereClause,
        include: {
          images: {
            take: 3,
            orderBy: { sortOrder: 'asc' }
          },
          category: {
            select: { id: true, name: true }
          },
          seller: {
            select: { id: true, firstName: true, lastName: true }
          },
          bids: {
            orderBy: { amount: 'desc' },
            take: 1
          }
        },
        take: limit,
        orderBy: [
          { createdAt: 'desc' },
          { bidCount: 'desc' }
        ]
      });

      // Get category name
      const category = await prisma.category.findUnique({
        where: { id: categoryId },
        select: { name: true }
      });

      return successResponse("Related products by category retrieved successfully", {
        relatedProducts,
        total: relatedProducts.length,
        relationshipType: 'category',
        categoryName: category?.name
      });

    } catch (error) {
      console.error("Get related products by category error:", error);
      return errorResponse("Failed to retrieve related products by category");
    }
  }

  /**
   * Get related products by seller
   */
  async getRelatedProductsBySeller(sellerId: string, excludeProductId?: string, limit: number = 6) {
    try {
      const whereClause: any = {
        sellerId,
        status: 'active',
        isActive: true
      };

      if (excludeProductId) {
        whereClause.id = { not: excludeProductId };
      }

      const relatedProducts = await prisma.product.findMany({
        where: whereClause,
        include: {
          images: {
            take: 3,
            orderBy: { sortOrder: 'asc' }
          },
          category: {
            select: { id: true, name: true }
          },
          seller: {
            select: { id: true, firstName: true, lastName: true }
          },
          bids: {
            orderBy: { amount: 'desc' },
            take: 1
          }
        },
        take: limit,
        orderBy: [
          { createdAt: 'desc' },
          { bidCount: 'desc' }
        ]
      });

      return successResponse("Related products by seller retrieved successfully", {
        relatedProducts,
        total: relatedProducts.length,
        relationshipType: 'seller'
      });

    } catch (error) {
      console.error("Get related products by seller error:", error);
      return errorResponse("Failed to retrieve related products by seller");
    }
  }
}

export default new ProductService();
