import { WebSocket, WebSocketServer } from 'ws';
import { EventEmitter } from 'events';

interface WebSocketClient {
  ws: WebSocket;
  userId?: string;
  productId?: string;
  subscriptions: Set<string>;
  lastHeartbeat: number;
  isAlive: boolean;
  connectionTime: number;
  reconnectAttempts: number;
  metadata: {
    userAgent?: string;
    ip?: string;
    origin?: string;
  };
}

interface BatchedMessage {
  channel: string;
  messages: any[];
  timestamp: number;
}

interface ConnectionStats {
  totalConnections: number;
  activeConnections: number;
  messagesSent: number;
  messagesReceived: number;
  reconnections: number;
  errors: number;
  uptime: number;
}

interface WebSocketConfig {
  heartbeatInterval: number;
  maxReconnectAttempts: number;
  messageQueueSize: number;
  enableBatching: boolean;
  batchInterval: number;
}

class WebSocketService extends EventEmitter {
  private wss: WebSocketServer | null = null;
  private clients: Map<string, WebSocketClient> = new Map();
  private messageQueue: Map<string, any[]> = new Map();
  private batchedMessages: Map<string, BatchedMessage> = new Map();
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private stats: ConnectionStats = {
    totalConnections: 0,
    activeConnections: 0,
    messagesSent: 0,
    messagesReceived: 0,
    reconnections: 0,
    errors: 0,
    uptime: Date.now()
  };

  private config: WebSocketConfig = {
    heartbeatInterval: 30000, // 30 seconds
    maxReconnectAttempts: 5,
    messageQueueSize: 100,
    enableBatching: false,
    batchInterval: 1000 // 1 second
  };

  /**
   * Initialize WebSocket server
   */
  initialize(server: any, config?: Partial<WebSocketConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }

    this.wss = new WebSocketServer({
      server,
      perMessageDeflate: {
        zlibDeflateOptions: {
          level: 3,
          chunkSize: 1024,
        },
        threshold: 1024,
        concurrencyLimit: 10,
      }
    });

    this.wss.on('connection', (ws: WebSocket, request) => {
      const clientId = this.generateClientId();
      const client: WebSocketClient = {
        ws,
        subscriptions: new Set(),
        lastHeartbeat: Date.now(),
        isAlive: true,
        connectionTime: Date.now(),
        reconnectAttempts: 0,
        metadata: {
          userAgent: request.headers['user-agent'],
          ip: request.socket.remoteAddress,
          origin: request.headers.origin
        }
      };

      this.clients.set(clientId, client);
      this.stats.totalConnections++;
      this.stats.activeConnections++;

      console.log(`WebSocket client connected: ${clientId} from ${client.metadata.ip}`);
      this.emit('clientConnected', { clientId, client });

      // Handle incoming messages
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.stats.messagesReceived++;
          client.lastHeartbeat = Date.now();
          this.handleMessage(clientId, message);
        } catch (error) {
          console.error('WebSocket message parse error:', error);
          this.stats.errors++;
          this.sendToClient(clientId, {
            type: 'error',
            message: 'Invalid message format'
          });
        }
      });

      // Handle pong responses
      ws.on('pong', () => {
        client.isAlive = true;
        client.lastHeartbeat = Date.now();
      });

      // Handle client disconnect
      ws.on('close', (code, reason) => {
        this.clients.delete(clientId);
        this.stats.activeConnections--;
        console.log(`WebSocket client disconnected: ${clientId}, code: ${code}, reason: ${reason}`);
        this.emit('clientDisconnected', { clientId, code, reason });
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error(`WebSocket error for client ${clientId}:`, error);
        this.stats.errors++;
        this.clients.delete(clientId);
        this.stats.activeConnections--;
        this.emit('clientError', { clientId, error });
      });

      // Send welcome message
      this.sendToClient(clientId, {
        type: 'connected',
        clientId,
        message: 'WebSocket connection established',
        serverTime: new Date().toISOString()
      });
    });

    // Start heartbeat mechanism
    this.startHeartbeat();

    console.log('WebSocket server initialized with enhanced features');
    this.emit('serverInitialized');
  }

  /**
   * Start heartbeat mechanism to detect dead connections
   */
  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      this.lastHeartbeatTime = new Date().toISOString();

      this.clients.forEach((client, clientId) => {
        if (!client.isAlive) {
          console.log(`Terminating dead connection: ${clientId}`);
          client.ws.terminate();
          this.clients.delete(clientId);
          this.stats.activeConnections--;
          return;
        }

        // Check if client hasn't responded to heartbeat in too long
        const timeSinceLastHeartbeat = Date.now() - client.lastHeartbeat;
        if (timeSinceLastHeartbeat > this.config.heartbeatInterval * 2) {
          console.log(`Client ${clientId} heartbeat timeout, terminating connection`);
          client.ws.terminate();
          this.clients.delete(clientId);
          this.stats.activeConnections--;
          return;
        }

        client.isAlive = false;
        client.ws.ping();
      });
    }, this.config.heartbeatInterval);
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (!client) {
      console.warn(`Message received from unknown client: ${clientId}`);
      return;
    }

    try {
      switch (message.type) {
        case 'subscribe':
          this.handleSubscribe(clientId, message);
          break;
        case 'unsubscribe':
          this.handleUnsubscribe(clientId, message);
          break;
        case 'authenticate':
          this.handleAuthenticate(clientId, message);
          break;
        case 'ping':
          this.handlePing(clientId, message);
          break;
        case 'get_stats':
          this.handleGetStats(clientId);
          break;
        default:
          console.log(`Unknown message type: ${message.type} from client: ${clientId}`);
          this.sendToClient(clientId, {
            type: 'error',
            message: `Unknown message type: ${message.type}`
          });
      }
    } catch (error) {
      console.error(`Error handling message from client ${clientId}:`, error);
      this.stats.errors++;
      this.sendToClient(clientId, {
        type: 'error',
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle ping messages
   */
  private handlePing(clientId: string, message: any) {
    this.sendToClient(clientId, {
      type: 'pong',
      timestamp: new Date().toISOString(),
      clientTime: message.timestamp
    });
  }

  /**
   * Handle stats request
   */
  private handleGetStats(clientId: string) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const clientStats = {
      connectionTime: client.connectionTime,
      subscriptions: Array.from(client.subscriptions),
      lastHeartbeat: client.lastHeartbeat,
      isAuthenticated: !!client.userId
    };

    this.sendToClient(clientId, {
      type: 'stats',
      serverStats: this.getStats(),
      clientStats
    });
  }

  /**
   * Handle subscription to channels
   */
  private handleSubscribe(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { channel, channels } = message;

    // Handle single channel subscription
    if (channel) {
      if (this.isValidChannel(channel, client)) {
        client.subscriptions.add(channel);
        console.log(`Client ${clientId} subscribed to ${channel}`);

        this.sendToClient(clientId, {
          type: 'subscribed',
          channel,
          message: `Successfully subscribed to ${channel}`,
          timestamp: new Date().toISOString()
        });

        this.emit('clientSubscribed', { clientId, channel });
      } else {
        this.sendToClient(clientId, {
          type: 'subscription_error',
          channel,
          message: `Access denied or invalid channel: ${channel}`
        });
      }
    }

    // Handle multiple channel subscriptions
    if (channels && Array.isArray(channels)) {
      const successful: string[] = [];
      const failed: string[] = [];

      channels.forEach((ch: string) => {
        if (this.isValidChannel(ch, client)) {
          client.subscriptions.add(ch);
          successful.push(ch);
        } else {
          failed.push(ch);
        }
      });

      this.sendToClient(clientId, {
        type: 'bulk_subscription_result',
        successful,
        failed,
        message: `Subscribed to ${successful.length} channels, ${failed.length} failed`
      });
    }
  }

  /**
   * Validate if client can subscribe to channel
   */
  private isValidChannel(channel: string, client: WebSocketClient): boolean {
    // Basic channel validation
    if (!channel || typeof channel !== 'string') {
      return false;
    }

    // Check if it's a user-specific channel
    if (channel.startsWith('user:')) {
      const userId = channel.split(':')[1];
      return client.userId === userId; // Only allow subscription to own user channel
    }

    // Product channels are public
    if (channel.startsWith('product:')) {
      return true;
    }

    // Global channels (with restrictions)
    const allowedGlobalChannels = ['global:announcements', 'global:system'];
    if (allowedGlobalChannels.includes(channel)) {
      return true;
    }

    return false;
  }

  /**
   * Handle unsubscription from channels
   */
  private handleUnsubscribe(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { channel, channels } = message;

    // Handle single channel unsubscription
    if (channel) {
      if (client.subscriptions.has(channel)) {
        client.subscriptions.delete(channel);
        console.log(`Client ${clientId} unsubscribed from ${channel}`);

        this.sendToClient(clientId, {
          type: 'unsubscribed',
          channel,
          message: `Successfully unsubscribed from ${channel}`,
          timestamp: new Date().toISOString()
        });

        this.emit('clientUnsubscribed', { clientId, channel });
      } else {
        this.sendToClient(clientId, {
          type: 'unsubscription_error',
          channel,
          message: `Not subscribed to ${channel}`
        });
      }
    }

    // Handle multiple channel unsubscriptions
    if (channels && Array.isArray(channels)) {
      const successful: string[] = [];
      const failed: string[] = [];

      channels.forEach((ch: string) => {
        if (client.subscriptions.has(ch)) {
          client.subscriptions.delete(ch);
          successful.push(ch);
        } else {
          failed.push(ch);
        }
      });

      this.sendToClient(clientId, {
        type: 'bulk_unsubscription_result',
        successful,
        failed,
        message: `Unsubscribed from ${successful.length} channels, ${failed.length} not found`
      });
    }

    // Handle unsubscribe all
    if (message.all === true) {
      const allChannels = Array.from(client.subscriptions);
      client.subscriptions.clear();

      this.sendToClient(clientId, {
        type: 'unsubscribed_all',
        channels: allChannels,
        message: `Unsubscribed from all ${allChannels.length} channels`
      });
    }
  }

  /**
   * Handle client authentication
   */
  private handleAuthenticate(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { userId, token, sessionId } = message;

    try {
      // TODO: Implement proper token validation
      // For now, we'll do basic validation
      if (userId && token) {
        // Validate token format (basic check)
        if (typeof token !== 'string' || token.length < 10) {
          this.sendToClient(clientId, {
            type: 'authentication_error',
            message: 'Invalid token format'
          });
          return;
        }

        // Check if user is already authenticated on another connection
        const existingClient = Array.from(this.clients.values())
          .find(c => c.userId === userId && c !== client);

        if (existingClient) {
          console.log(`User ${userId} already connected, allowing multiple sessions`);
        }

        client.userId = userId;
        console.log(`Client ${clientId} authenticated as user ${userId}`);

        this.sendToClient(clientId, {
          type: 'authenticated',
          userId,
          sessionId,
          message: 'Authentication successful',
          timestamp: new Date().toISOString()
        });

        this.emit('clientAuthenticated', { clientId, userId });

        // Auto-subscribe to user-specific channel
        const userChannel = `user:${userId}`;
        client.subscriptions.add(userChannel);

        this.sendToClient(clientId, {
          type: 'auto_subscribed',
          channel: userChannel,
          message: `Auto-subscribed to ${userChannel}`
        });

      } else {
        this.sendToClient(clientId, {
          type: 'authentication_error',
          message: 'Missing userId or token'
        });
      }
    } catch (error) {
      console.error(`Authentication error for client ${clientId}:`, error);
      this.sendToClient(clientId, {
        type: 'authentication_error',
        message: 'Authentication failed'
      });
    }
  }

  /**
   * Send message to specific client
   */
  private sendToClient(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      try {
        const messageStr = JSON.stringify(message);
        client.ws.send(messageStr);
        this.stats.messagesSent++;
      } catch (error) {
        console.error(`Error sending message to client ${clientId}:`, error);
        this.stats.errors++;
      }
    }
  }

  /**
   * Broadcast to all clients subscribed to a channel
   */
  public broadcast(channel: string, message: any, excludeClientId?: string) {
    console.log(`📡 Broadcasting to channel: ${channel}`);
    console.log(`📦 Message data:`, message);

    const broadcastMessage = {
      type: 'broadcast',
      channel,
      data: message,
      timestamp: new Date().toISOString()
    };

    let sentCount = 0;
    let eligibleCount = 0;
    const messageStr = JSON.stringify(broadcastMessage);

    console.log(`🔍 Checking ${this.clients.size} total clients for channel subscription...`);

    this.clients.forEach((client, clientId) => {
      const isSubscribed = client.subscriptions.has(channel);
      const isOpen = client.ws.readyState === WebSocket.OPEN;
      const isNotExcluded = clientId !== excludeClientId;

      console.log(`👤 Client ${clientId}: subscribed=${isSubscribed}, open=${isOpen}, notExcluded=${isNotExcluded}, userId=${client.userId}`);

      if (isSubscribed && isOpen && isNotExcluded) {
        eligibleCount++;
        try {
          client.ws.send(messageStr);
          sentCount++;
          this.stats.messagesSent++;
          console.log(`✅ Message sent to client ${clientId} (userId: ${client.userId})`);
        } catch (error) {
          console.error(`❌ Error broadcasting to client ${clientId}:`, error);
          this.stats.errors++;
        }
      }
    });

    console.log(`📊 Broadcast summary - Channel: ${channel}, Eligible: ${eligibleCount}, Sent: ${sentCount}, Total clients: ${this.clients.size}`);
    this.emit('messageBroadcast', { channel, message, sentCount });
  }

  /**
   * Send message to specific user (all their connections)
   */
  public sendToUser(userId: string, message: any, excludeClientId?: string) {
    let sentCount = 0;
    const userMessage = {
      type: 'user_message',
      data: message,
      timestamp: new Date().toISOString()
    };
    const messageStr = JSON.stringify(userMessage);

    this.clients.forEach((client, clientId) => {
      if (
        client.userId === userId &&
        client.ws.readyState === WebSocket.OPEN &&
        clientId !== excludeClientId
      ) {
        try {
          client.ws.send(messageStr);
          sentCount++;
          this.stats.messagesSent++;
        } catch (error) {
          console.error(`Error sending to user ${userId}, client ${clientId}:`, error);
          this.stats.errors++;
        }
      }
    });

    console.log(`Sent message to user ${userId}: ${sentCount} connections received message`);
    this.emit('userMessage', { userId, message, sentCount });
  }

  /**
   * Notify about new bid with enhanced data
   */
  public notifyNewBid(productId: string, bidData: any) {
    console.log('🔔 WebSocket notifyNewBid called with:', { productId, bidData });

    const bidUpdate = {
      type: 'new_bid',
      productId,
      currentBid: bidData.amount || bidData.currentBid,
      bidderName: bidData.bidderName || bidData.userName || 'Anonymous',
      bidCount: bidData.bidCount || 0,
      timestamp: new Date().toISOString(),
      bid: bidData
    };

    console.log(`📤 Broadcasting new bid for product ${productId}:`, bidUpdate);
    console.log(`👥 Current active connections: ${this.clients.size}`);

    // Check if there are any clients subscribed to this product
    const subscribedClients = Array.from(this.clients.entries()).filter(([clientId, client]) =>
      client.subscriptions.has(`product:${productId}`)
    );
    console.log(`🎯 Clients subscribed to product:${productId}: ${subscribedClients.length}`);

    if (subscribedClients.length === 0) {
      console.log('⚠️  No clients subscribed to this product channel!');
    } else {
      subscribedClients.forEach(([clientId, client]) => {
        console.log(`📱 Client ${clientId} subscribed to product:${productId} (userId: ${client.userId})`);
      });
    }

    this.broadcast(`product:${productId}`, bidUpdate);

    // Also broadcast to general bid updates channel
    this.broadcast('bid_updates', bidUpdate);

    console.log('✅ WebSocket broadcast completed');
  }

  /**
   * Notify about auto-bid activation
   */
  public notifyAutoBidActivated(userId: string, productId: string, autoBidData: any) {
    this.sendToUser(userId, {
      type: 'auto_bid_activated',
      productId,
      autoBid: autoBidData
    });

    this.broadcast(`product:${productId}`, {
      type: 'auto_bid_status',
      productId,
      message: 'Auto-bid activated by a user'
    });
  }

  /**
   * Notify about auto-bid execution
   */
  public notifyAutoBidExecuted(userId: string, productId: string, bidData: any) {
    this.sendToUser(userId, {
      type: 'auto_bid_executed',
      productId,
      bid: bidData,
      message: 'Your auto-bid has been executed'
    });

    this.broadcast(`product:${productId}`, {
      type: 'auto_bid_executed',
      productId,
      bid: bidData
    });
  }

  /**
   * Notify about auction status changes
   */
  public notifyAuctionStatusChange(productId: string, status: string, data: any) {
    this.broadcast(`product:${productId}`, {
      type: 'auction_status_change',
      productId,
      status,
      data
    });
  }

  /**
   * Notify about auction extension
   */
  public notifyAuctionExtended(productId: string, extensionData: any) {
    this.broadcast(`product:${productId}`, {
      type: 'auction_extended',
      productId,
      data: extensionData
    });
  }

  /**
   * Generate unique client ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Get connection statistics
   */
  public getStats(): ConnectionStats {
    const totalClients = this.clients.size;
    const authenticatedClients = Array.from(this.clients.values()).filter(c => c.userId).length;
    const totalSubscriptions = Array.from(this.clients.values()).reduce((sum, c) => sum + c.subscriptions.size, 0);

    return {
      ...this.stats,
      activeConnections: totalClients,
      uptime: Date.now() - this.stats.uptime,
      totalSubscriptions,
      authenticatedClients,
      channels: this.getActiveChannels()
    } as ConnectionStats & { totalSubscriptions: number; authenticatedClients: number; channels: string[] };
  }

  /**
   * Get basic connection statistics for health checks
   */
  public getConnectionStats() {
    const clients = Array.from(this.clients.values());
    const activeClients = clients.filter(client => client.isAlive);

    return {
      totalConnections: this.clients.size,
      activeConnections: activeClients.length,
      inactiveConnections: this.clients.size - activeClients.length,
      totalSubscriptions: clients.reduce((acc, client) => acc + client.subscriptions.size, 0),
      averageConnectionTime: clients.length > 0
        ? Math.round(clients.reduce((acc, client) => acc + (Date.now() - client.connectionTime), 0) / clients.length / 1000)
        : 0,
      reconnectAttempts: clients.reduce((acc, client) => acc + client.reconnectAttempts, 0)
    }
  }

  /**
   * Get detailed metrics for monitoring and debugging
   */
  public getDetailedMetrics() {
    const clients = Array.from(this.clients.values());
    const now = Date.now();

    // Connection distribution by user
    const userConnections = new Map<string, number>();
    const productSubscriptions = new Map<string, number>();
    const connectionsByAge = {
      under1min: 0,
      under5min: 0,
      under15min: 0,
      under1hour: 0,
      over1hour: 0
    };

    clients.forEach(client => {
      // User connections
      if (client.userId) {
        userConnections.set(client.userId, (userConnections.get(client.userId) || 0) + 1);
      }

      // Product subscriptions
      client.subscriptions.forEach(subscription => {
        if (subscription.startsWith('product:')) {
          const productId = subscription.split(':')[1];
          productSubscriptions.set(productId, (productSubscriptions.get(productId) || 0) + 1);
        }
      });

      // Connection age distribution
      const ageInMinutes = (now - client.connectionTime) / (1000 * 60);
      if (ageInMinutes < 1) connectionsByAge.under1min++;
      else if (ageInMinutes < 5) connectionsByAge.under5min++;
      else if (ageInMinutes < 15) connectionsByAge.under15min++;
      else if (ageInMinutes < 60) connectionsByAge.under1hour++;
      else connectionsByAge.over1hour++;
    });

    return {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      connections: this.getConnectionStats(),
      distribution: {
        userConnections: Object.fromEntries(userConnections),
        productSubscriptions: Object.fromEntries(productSubscriptions),
        connectionsByAge
      },
      performance: {
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        eventLoopDelay: this.getEventLoopDelay()
      },
      health: {
        status: this.clients.size > 0 ? 'active' : 'idle',
        lastHeartbeat: this.lastHeartbeatTime,
        heartbeatInterval: this.heartbeatInterval ? 'active' : 'inactive',
        maxReconnectAttempts: this.config.maxReconnectAttempts
      },
      stats: this.stats
    }
  }

  /**
   * Get event loop delay (simple approximation)
   */
  private getEventLoopDelay(): number {
    const start = process.hrtime.bigint();
    setImmediate(() => {
      const delay = Number(process.hrtime.bigint() - start) / 1000000; // Convert to milliseconds
      this.eventLoopDelay = delay;
    });
    return this.eventLoopDelay || 0;
  }

  private eventLoopDelay: number = 0;
  private lastHeartbeatTime: string = new Date().toISOString();

  /**
   * Get list of active channels
   */
  private getActiveChannels(): string[] {
    const channels = new Set<string>();
    this.clients.forEach(client => {
      client.subscriptions.forEach(channel => channels.add(channel));
    });
    return Array.from(channels);
  }

  /**
   * Get detailed client information
   */
  public getClientInfo(clientId: string) {
    const client = this.clients.get(clientId);
    if (!client) return null;

    return {
      clientId,
      userId: client.userId,
      subscriptions: Array.from(client.subscriptions),
      connectionTime: client.connectionTime,
      lastHeartbeat: client.lastHeartbeat,
      isAlive: client.isAlive,
      metadata: client.metadata
    };
  }

  /**
   * Close all connections gracefully
   */
  public close() {
    console.log('🛑 Shutting down WebSocket service...');

    // Clear heartbeat interval
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    // Send shutdown notification to all clients
    this.clients.forEach((client) => {
      if (client.ws.readyState === WebSocket.OPEN) {
        try {
          client.ws.send(JSON.stringify({
            type: 'server_shutdown',
            message: 'Server is shutting down',
            timestamp: new Date().toISOString()
          }));
        } catch (error) {
          console.error('Error sending shutdown message:', error);
        }
      }
    });

    // Close all client connections
    this.clients.forEach((client) => {
      try {
        client.ws.close(1001, 'Server shutdown');
      } catch (error) {
        console.error('Error closing client connection:', error);
      }
    });

    this.clients.clear();

    // Close WebSocket server
    if (this.wss) {
      this.wss.close(() => {
        console.log('✅ WebSocket server closed');
      });
    }

    this.emit('serverClosed');
    console.log('✅ WebSocket service shutdown complete');
  }
}

// Export singleton instance
const webSocketService = new WebSocketService();
export default webSocketService;
