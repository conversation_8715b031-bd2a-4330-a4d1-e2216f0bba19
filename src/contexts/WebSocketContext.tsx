'use client'

import React, { createContext, useContext, useEffect, useRef, useState, useCallback, ReactNode } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useSession } from 'next-auth/react'

interface WebSocketMessage {
  type: string
  channel?: string
  data: any
  timestamp: string
}

interface WebSocketContextType {
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  subscribe: (channel: string, callback?: (data: any) => void) => void
  unsubscribe: (channel: string, callback?: (data: any) => void) => void
  sendMessage: (message: any) => void
  lastMessage: WebSocketMessage | null
  connectionStats: {
    reconnectAttempts: number
    messagesReceived: number
    messagesSent: number
    uptime: number
    latency: number
    connectionQuality: 'excellent' | 'good' | 'poor' | 'unknown'
  }
  reconnect: () => void
  disconnect: () => void
  // Enhanced methods for better stability
  subscribeToProduct: (productId: string, callback?: (data: any) => void) => () => void
  getConnectionHealth: () => {
    isHealthy: boolean
    lastPingTime: number
    averageLatency: number
    errorRate: number
  }
}

interface WebSocketProviderProps {
  children: ReactNode
  wsUrl?: string
  autoConnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
}

const WebSocketContext = createContext<WebSocketContextType | null>(null)

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({
  children,
  wsUrl,
  autoConnect = true,
  reconnectInterval = 3000,
  maxReconnectAttempts = 5
}) => {
  const { data: session } = useSession()
  const queryClient = useQueryClient()
  
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const connectionTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const subscriptionsRef = useRef<Set<string>>(new Set())
  const subscribersRef = useRef<Map<string, Set<(data: any) => void>>>(new Map())
  const messageQueueRef = useRef<any[]>([])
  const sessionRef = useRef(session)
  const reconnectAttemptsRef = useRef(0)
  const isInitializedRef = useRef(false)

  const [isConnected, setIsConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null)
  const [connectionStats, setConnectionStats] = useState({
    reconnectAttempts: 0,
    messagesReceived: 0,
    messagesSent: 0,
    uptime: 0,
    latency: 0,
    connectionQuality: 'unknown' as 'excellent' | 'good' | 'poor' | 'unknown'
  })

  // Enhanced connection monitoring
  const pingTimesRef = useRef<number[]>([])
  const lastPingTimeRef = useRef<number>(0)
  const errorCountRef = useRef<number>(0)
  const connectionHealthRef = useRef({
    isHealthy: true,
    lastPingTime: 0,
    averageLatency: 0,
    errorRate: 0
  })

  const connectStartTime = useRef<number>(0)

  // Update refs when session changes
  useEffect(() => {
    sessionRef.current = session
  }, [session])

  // Handle pong with latency calculation
  const handlePong = useCallback((timestamp: number) => {
    const latency = Date.now() - timestamp

    // Keep last 10 ping times for average calculation
    pingTimesRef.current.push(latency)
    if (pingTimesRef.current.length > 10) {
      pingTimesRef.current.shift()
    }

    const avgLatency = pingTimesRef.current.reduce((a, b) => a + b, 0) / pingTimesRef.current.length

    // Determine connection quality based on latency
    let quality: 'excellent' | 'good' | 'poor' | 'unknown' = 'unknown'
    if (avgLatency < 100) quality = 'excellent'
    else if (avgLatency < 300) quality = 'good'
    else quality = 'poor'

    setConnectionStats(prev => ({
      ...prev,
      latency: avgLatency,
      connectionQuality: quality
    }))
  }, [])

  // Enhanced ping with latency measurement
  const sendPing = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const pingTime = Date.now()
      lastPingTimeRef.current = pingTime

      wsRef.current.send(JSON.stringify({
        type: 'ping',
        timestamp: pingTime
      }))
    }
  }, [])

  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'broadcast':
        if (message.channel?.startsWith('product:')) {
          const productId = message.channel.split(':')[1]

          if (message.data.type === 'new_bid') {
            // Invalidate product queries for real-time bid updates
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', productId]
            })
            queryClient.invalidateQueries({
              queryKey: ['products', 'slug']
            })
            queryClient.invalidateQueries({
              queryKey: ['bidding', 'history', productId]
            })
          } else if (message.data.type === 'auction_extended') {
            // Invalidate product queries for auction extension updates
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', productId]
            })
            queryClient.invalidateQueries({
              queryKey: ['products', 'slug']
            })
            queryClient.invalidateQueries({
              queryKey: ['auction-extension', 'logs', productId]
            })
            queryClient.invalidateQueries({
              queryKey: ['auction-extension', 'stats', productId]
            })
          }
        }
        break

      case 'user_message':
        if (message.data.type === 'auto_bid_executed') {
          const productId = message.data.productId
          queryClient.invalidateQueries({
            queryKey: ['products', 'detail', productId]
          })
          queryClient.invalidateQueries({
            queryKey: ['bidding', 'history', productId]
          })
          queryClient.invalidateQueries({
            queryKey: ['auto-bid', productId]
          })
        }
        break

      case 'pong':
        // Handle pong response for latency measurement
        if (message.data?.timestamp) {
          handlePong(message.data.timestamp)
        }
        break
    }
  }, [queryClient, handlePong])

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('🔗 WebSocket already connected, skipping connection attempt')
      return
    }

    // Prevent multiple connection attempts
    if (wsRef.current?.readyState === WebSocket.CONNECTING) {
      console.log('🔗 WebSocket already connecting, skipping connection attempt')
      return
    }

    const url = wsUrl || process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'
    console.log(`🔗 Attempting to connect to WebSocket: ${url}`)

    try {
      setConnectionStatus('connecting')
      wsRef.current = new WebSocket(url)
      connectStartTime.current = Date.now()

      // Set connection timeout with retry logic
      connectionTimeoutRef.current = setTimeout(() => {
        if (wsRef.current?.readyState === WebSocket.CONNECTING) {
          console.warn('WebSocket connection timeout')
          wsRef.current?.close()

          // Retry connection if under max attempts
          if (reconnectAttemptsRef.current < maxReconnectAttempts) {
            const delay = Math.min(2000 * Math.pow(1.5, reconnectAttemptsRef.current), 15000)
            console.log(`Connection timeout, retrying in ${delay}ms (attempt ${reconnectAttemptsRef.current + 1})`)

            reconnectAttemptsRef.current += 1
            setConnectionStats(prev => ({
              ...prev,
              reconnectAttempts: reconnectAttemptsRef.current
            }))

            reconnectTimeoutRef.current = setTimeout(() => {
              connect()
            }, delay)
          } else {
            setConnectionStatus('error')
            console.error('Connection timeout - max attempts reached')
          }
        }
      }, 8000) // 8 second timeout

      wsRef.current.onopen = () => {
        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = undefined
        }
        console.log('WebSocket connected successfully')
        setIsConnected(true)
        setConnectionStatus('connected')

        // Reset reconnect attempts on successful connection
        reconnectAttemptsRef.current = 0
        setConnectionStats(prev => ({
          ...prev,
          reconnectAttempts: 0,
          uptime: Date.now() - connectStartTime.current
        }))

        // Authenticate if user is logged in
        const currentSession = sessionRef.current
        if (currentSession?.user?.id) {
          wsRef.current?.send(JSON.stringify({
            type: 'authenticate',
            userId: currentSession.user.id,
            token: currentSession.accessToken || 'temp-token',
            sessionId: currentSession.user.id
          }))
        }

        // Re-subscribe to all channels
        subscriptionsRef.current.forEach(channel => {
          wsRef.current?.send(JSON.stringify({
            type: 'subscribe',
            channel
          }))
        })

        // Send queued messages
        while (messageQueueRef.current.length > 0) {
          const queuedMessage = messageQueueRef.current.shift()
          wsRef.current?.send(JSON.stringify(queuedMessage))
        }
      }

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          setLastMessage(message)
          setConnectionStats(prev => ({
            ...prev,
            messagesReceived: prev.messagesReceived + 1,
            uptime: Date.now() - connectStartTime.current
          }))

          // Handle different message types
          handleWebSocketMessage(message)

          // Handle channel-specific subscribers
          if (message.type === 'broadcast' && message.channel) {
            const channelSubscribers = subscribersRef.current.get(message.channel) || new Set()
            channelSubscribers.forEach(callback => {
              try {
                callback(message.data)
              } catch (error) {
                console.error('Error in subscriber callback:', error)
              }
            })
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      wsRef.current.onclose = (event) => {
        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = undefined
        }

        console.log(`WebSocket disconnected ${event.code}`)
        setIsConnected(false)
        setConnectionStatus('disconnected')

        // Handle different close codes
        if (event.code === 1000) {
          // Normal closure - don't reconnect
          console.log('WebSocket closed normally')
          return
        }

        if (event.code === 1006 || event.code === 1005) {
          // Abnormal closure - attempt reconnection with backoff
          if (reconnectAttemptsRef.current < maxReconnectAttempts) {
            const delay = Math.min(3000 * Math.pow(1.8, reconnectAttemptsRef.current), 30000)
            console.log(`Connection lost (${event.code}), reconnecting in ${delay}ms (attempt ${reconnectAttemptsRef.current + 1})`)

            reconnectAttemptsRef.current += 1
            setConnectionStats(prev => ({
              ...prev,
              reconnectAttempts: reconnectAttemptsRef.current
            }))

            reconnectTimeoutRef.current = setTimeout(() => {
              connect()
            }, delay)
          } else {
            console.log('Max reconnection attempts reached')
            setConnectionStatus('error')
          }
        }
      }

      wsRef.current.onerror = (error) => {
        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = undefined
        }

        console.error('WebSocket error:', error)

        // Don't immediately set error state, let onclose handle reconnection
        if (wsRef.current?.readyState === WebSocket.CONNECTING) {
          console.log('Connection failed - will retry via onclose handler')
        }
      }
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      setConnectionStatus('error')
    }
  }, [wsUrl, maxReconnectAttempts, handleWebSocketMessage])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = undefined
    }

    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current)
      connectionTimeoutRef.current = undefined
    }

    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }

    setIsConnected(false)
    setConnectionStatus('disconnected')
  }, [])

  const subscribe = useCallback((channel: string, callback?: (data: any) => void) => {
    console.log(`🔔 Subscribing to channel: ${channel}`)
    subscriptionsRef.current.add(channel)

    // Add callback to subscribers if provided
    if (callback) {
      if (!subscribersRef.current.has(channel)) {
        subscribersRef.current.set(channel, new Set())
      }
      subscribersRef.current.get(channel)?.add(callback)
      console.log(`📝 Added callback for channel: ${channel}`)
    }

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'subscribe',
        channel
      }))
      console.log(`✅ Sent subscribe message for: ${channel}`)
    } else {
      console.log(`⏳ WebSocket not ready, subscription queued for: ${channel}`)
    }
  }, [])

  const unsubscribe = useCallback((channel: string, callback?: (data: any) => void) => {
    console.log(`🔕 Unsubscribing from channel: ${channel}`)

    // Remove callback from subscribers if provided
    if (callback) {
      const channelSubscribers = subscribersRef.current.get(channel)
      if (channelSubscribers) {
        channelSubscribers.delete(callback)
        console.log(`🗑️ Removed callback for channel: ${channel}`)

        // Only unsubscribe if no more callbacks
        if (channelSubscribers.size === 0) {
          subscribersRef.current.delete(channel)
          subscriptionsRef.current.delete(channel)

          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
              type: 'unsubscribe',
              channel
            }))
            console.log(`❌ Sent unsubscribe message for: ${channel}`)
          }
        }
      }
    } else {
      // Remove all subscribers for this channel
      subscribersRef.current.delete(channel)
      subscriptionsRef.current.delete(channel)

      if (wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({
          type: 'unsubscribe',
          channel
        }))
        console.log(`❌ Sent unsubscribe message for: ${channel}`)
      }
    }
  }, [])

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
      setConnectionStats(prev => ({
        ...prev,
        messagesSent: prev.messagesSent + 1
      }))
    } else {
      // Queue message for when connection is restored
      messageQueueRef.current.push(message)
      console.warn('WebSocket is not connected, message queued')
    }
  }, [])

  // Initialize connection only once
  useEffect(() => {
    console.log(`🚀 WebSocket initialization effect - autoConnect: ${autoConnect}, isInitialized: ${isInitializedRef.current}`)

    if (autoConnect && !isInitializedRef.current) {
      console.log('🚀 Initializing WebSocket connection for the first time')
      isInitializedRef.current = true
      connect()
    }

    return () => {
      if (isInitializedRef.current) {
        console.log('🧹 Cleaning up WebSocket connection')
        disconnect()
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoConnect]) // Only depend on autoConnect to prevent infinite re-renders

  // Update uptime periodically and send pings
  useEffect(() => {
    if (isConnected) {
      const uptimeInterval = setInterval(() => {
        setConnectionStats(prev => ({
          ...prev,
          uptime: Date.now() - connectStartTime.current
        }))
      }, 1000)

      // Send ping every 30 seconds to measure latency
      const pingInterval = setInterval(() => {
        sendPing()
      }, 30000)

      return () => {
        clearInterval(uptimeInterval)
        clearInterval(pingInterval)
      }
    }
  }, [isConnected, sendPing])

  const reconnect = useCallback(() => {
    // Reset reconnection attempts and force reconnect
    reconnectAttemptsRef.current = 0
    setConnectionStats(prev => ({ ...prev, reconnectAttempts: 0 }))
    disconnect()
    setTimeout(() => connect(), 100)
  }, [connect, disconnect])

  // Enhanced product subscription with automatic cleanup
  const subscribeToProduct = useCallback((productId: string, callback?: (data: any) => void) => {
    const channel = `product:${productId}`
    console.log(`🎯 Enhanced product subscription for: ${productId}`)

    subscribe(channel, callback)

    // Return cleanup function
    return () => {
      console.log(`🧹 Cleaning up product subscription for: ${productId}`)
      unsubscribe(channel, callback)
    }
  }, [subscribe, unsubscribe])

  // Get connection health metrics
  const getConnectionHealth = useCallback(() => {
    const avgLatency = pingTimesRef.current.length > 0
      ? pingTimesRef.current.reduce((a, b) => a + b, 0) / pingTimesRef.current.length
      : 0

    const errorRate = errorCountRef.current / Math.max(connectionStats.messagesReceived, 1)

    connectionHealthRef.current = {
      isHealthy: isConnected && avgLatency < 1000 && errorRate < 0.1,
      lastPingTime: lastPingTimeRef.current,
      averageLatency: avgLatency,
      errorRate: errorRate
    }

    return connectionHealthRef.current
  }, [isConnected, connectionStats.messagesReceived])



  const value: WebSocketContextType = {
    isConnected,
    connectionStatus,
    subscribe,
    unsubscribe,
    sendMessage,
    lastMessage,
    connectionStats,
    reconnect,
    disconnect,
    subscribeToProduct,
    getConnectionHealth
  }

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}

export const useWebSocketContext = () => {
  const context = useContext(WebSocketContext)
  return context // Return null if not within provider instead of throwing
}

export const useWebSocketContextRequired = () => {
  const context = useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocketContextRequired must be used within a WebSocketProvider')
  }
  return context
}
