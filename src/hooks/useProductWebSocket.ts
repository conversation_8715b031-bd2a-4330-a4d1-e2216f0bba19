'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import { useWebSocketContextRequired } from '@/contexts/WebSocketContext'
import { useQueryClient } from '@tanstack/react-query'

interface ProductWebSocketOptions {
  productId: string
  onBidUpdate?: (data: any) => void
  onAuctionExtended?: (data: any) => void
  onAuctionEnded?: (data: any) => void
  onError?: (error: any) => void
  enableAutoReconnect?: boolean
  maxRetries?: number
  retryDelay?: number
}

interface ProductWebSocketState {
  isSubscribed: boolean
  lastBidUpdate: any | null
  connectionQuality: 'excellent' | 'good' | 'poor' | 'unknown'
  subscriptionHealth: {
    messagesReceived: number
    lastMessageTime: number
    errorCount: number
  }
}

export const useProductWebSocket = (options: ProductWebSocketOptions) => {
  const {
    productId,
    onBidUpdate,
    onAuctionExtended,
    onAuctionEnded,
    onError,
    enableAutoReconnect = true,
    maxRetries = 3,
    retryDelay = 2000
  } = options

  const { 
    isConnected, 
    connectionStatus, 
    subscribeToProduct, 
    getConnectionHealth,
    connectionStats 
  } = useWebSocketContextRequired()
  
  const queryClient = useQueryClient()
  
  // State management
  const [state, setState] = useState<ProductWebSocketState>({
    isSubscribed: false,
    lastBidUpdate: null,
    connectionQuality: 'unknown',
    subscriptionHealth: {
      messagesReceived: 0,
      lastMessageTime: 0,
      errorCount: 0
    }
  })

  // Refs for stable references
  const retryCountRef = useRef(0)
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const unsubscribeRef = useRef<(() => void) | null>(null)
  const lastHealthCheckRef = useRef<number>(0)

  // Enhanced message handler with error handling and retry logic
  const handleProductMessage = useCallback((data: any) => {
    try {
      console.log(`📨 Enhanced product WebSocket message for ${productId}:`, data)
      
      // Update subscription health
      setState(prev => ({
        ...prev,
        subscriptionHealth: {
          ...prev.subscriptionHealth,
          messagesReceived: prev.subscriptionHealth.messagesReceived + 1,
          lastMessageTime: Date.now()
        },
        lastBidUpdate: data.type === 'new_bid' ? data : prev.lastBidUpdate
      }))

      // Handle different message types
      switch (data.type) {
        case 'new_bid':
          console.log(`💰 New bid received for product ${productId}:`, data)
          
          // Invalidate relevant queries
          queryClient.invalidateQueries({
            queryKey: ['products', 'detail', productId]
          })
          queryClient.invalidateQueries({
            queryKey: ['bidding', 'history', productId]
          })
          queryClient.invalidateQueries({
            queryKey: ['bidding', 'user-bid', productId]
          })
          
          onBidUpdate?.(data)
          break

        case 'auction_extended':
          console.log(`⏰ Auction extended for product ${productId}:`, data)
          
          // Invalidate auction-related queries
          queryClient.invalidateQueries({
            queryKey: ['products', 'detail', productId]
          })
          queryClient.invalidateQueries({
            queryKey: ['auction-extension', 'logs', productId]
          })
          
          onAuctionExtended?.(data)
          break

        case 'auction_ended':
          console.log(`🏁 Auction ended for product ${productId}:`, data)
          
          // Invalidate all product-related queries
          queryClient.invalidateQueries({
            queryKey: ['products', 'detail', productId]
          })
          queryClient.invalidateQueries({
            queryKey: ['bidding', 'history', productId]
          })
          
          onAuctionEnded?.(data)
          break

        default:
          console.log(`📋 Other message type for product ${productId}:`, data.type)
      }

      // Reset retry count on successful message
      retryCountRef.current = 0

    } catch (error) {
      console.error(`❌ Error handling product WebSocket message for ${productId}:`, error)
      
      setState(prev => ({
        ...prev,
        subscriptionHealth: {
          ...prev.subscriptionHealth,
          errorCount: prev.subscriptionHealth.errorCount + 1
        }
      }))
      
      onError?.(error)
    }
  }, [productId, queryClient, onBidUpdate, onAuctionExtended, onAuctionEnded, onError])

  // Enhanced subscription with retry logic
  const subscribe = useCallback(() => {
    if (!productId || !isConnected) {
      console.log(`⏳ Cannot subscribe to product ${productId}: not connected or no productId`)
      return
    }

    try {
      console.log(`🔔 Enhanced subscribing to product: ${productId}`)
      
      // Clean up existing subscription
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
      }

      // Create new subscription
      const unsubscribe = subscribeToProduct(productId, handleProductMessage)
      unsubscribeRef.current = unsubscribe

      setState(prev => ({
        ...prev,
        isSubscribed: true
      }))

      console.log(`✅ Successfully subscribed to product: ${productId}`)

    } catch (error) {
      console.error(`❌ Failed to subscribe to product ${productId}:`, error)
      
      setState(prev => ({
        ...prev,
        isSubscribed: false,
        subscriptionHealth: {
          ...prev.subscriptionHealth,
          errorCount: prev.subscriptionHealth.errorCount + 1
        }
      }))

      // Retry logic
      if (enableAutoReconnect && retryCountRef.current < maxRetries) {
        retryCountRef.current++
        console.log(`🔄 Retrying subscription for product ${productId} (attempt ${retryCountRef.current}/${maxRetries})`)
        
        retryTimeoutRef.current = setTimeout(() => {
          subscribe()
        }, retryDelay * retryCountRef.current) // Exponential backoff
      }

      onError?.(error)
    }
  }, [productId, isConnected, subscribeToProduct, handleProductMessage, enableAutoReconnect, maxRetries, retryDelay, onError])

  // Cleanup function
  const cleanup = useCallback(() => {
    console.log(`🧹 Cleaning up enhanced product WebSocket for: ${productId}`)
    
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current)
      retryTimeoutRef.current = null
    }

    if (unsubscribeRef.current) {
      unsubscribeRef.current()
      unsubscribeRef.current = null
    }

    setState(prev => ({
      ...prev,
      isSubscribed: false
    }))
  }, [productId])

  // Monitor connection health and update state
  useEffect(() => {
    const healthCheckInterval = setInterval(() => {
      const health = getConnectionHealth()
      const now = Date.now()
      
      setState(prev => ({
        ...prev,
        connectionQuality: connectionStats.connectionQuality
      }))

      // Check if we haven't received messages for too long
      const timeSinceLastMessage = now - state.subscriptionHealth.lastMessageTime
      if (state.isSubscribed && timeSinceLastMessage > 60000) { // 1 minute
        console.warn(`⚠️ No messages received for product ${productId} in ${timeSinceLastMessage}ms`)
        
        // Attempt to resubscribe if auto-reconnect is enabled
        if (enableAutoReconnect && isConnected) {
          console.log(`🔄 Attempting to resubscribe to product ${productId} due to message timeout`)
          subscribe()
        }
      }

      lastHealthCheckRef.current = now
    }, 30000) // Check every 30 seconds

    return () => clearInterval(healthCheckInterval)
  }, [getConnectionHealth, connectionStats.connectionQuality, state.subscriptionHealth.lastMessageTime, state.isSubscribed, productId, enableAutoReconnect, isConnected, subscribe])

  // Main subscription effect
  useEffect(() => {
    if (isConnected && productId) {
      subscribe()
    } else {
      cleanup()
    }

    return cleanup
  }, [isConnected, productId, subscribe, cleanup])

  // Cleanup on unmount
  useEffect(() => {
    return cleanup
  }, [cleanup])

  return {
    ...state,
    isConnected,
    connectionStatus,
    connectionQuality: connectionStats.connectionQuality,
    resubscribe: subscribe,
    cleanup
  }
}
