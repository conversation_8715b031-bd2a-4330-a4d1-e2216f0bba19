import { useState, useEffect, useCallback, useRef } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useWebSocketContextRequired } from '@/contexts/WebSocketContext'

interface BidUpdate {
  productId: string
  currentBid: number
  bidderName?: string
  bidCount: number
  timestamp: string
  type: 'new_bid' | 'auction_extended' | 'auction_ended'
}

interface RealtimeBiddingState {
  currentBid: number | null
  bidCount: number
  lastBidder?: string
  lastUpdate?: string
  isConnected: boolean
  error?: string
}

export const useRealtimeBidding = (productId: string, initialBid?: number) => {
  const { isConnected, subscribe, unsubscribe } = useWebSocketContextRequired()
  const queryClient = useQueryClient()
  const productIdRef = useRef(productId)

  const [state, setState] = useState<RealtimeBiddingState>({
    currentBid: initialBid || null,
    bidCount: 0,
    isConnected: false
  })

  // Update ref when productId changes
  useEffect(() => {
    productIdRef.current = productId
  }, [productId])

  // Handle bid updates from WebSocket - stable callback
  const handleBidUpdate = useCallback((data: BidUpdate) => {
    console.log('Received bid update:', data)

    setState(prev => ({
      ...prev,
      currentBid: data.currentBid,
      bidCount: data.bidCount,
      lastBidder: data.bidderName,
      lastUpdate: data.timestamp,
      error: undefined
    }))

    // Invalidate relevant queries to update UI using current productId
    const currentProductId = productIdRef.current
    queryClient.invalidateQueries({
      queryKey: ['products', 'detail', currentProductId]
    })
    queryClient.invalidateQueries({
      queryKey: ['bidding', 'history', currentProductId]
    })
  }, [queryClient])

  // Subscribe to product-specific channel
  useEffect(() => {
    if (!productId || !isConnected) {
      setState(prev => ({ ...prev, isConnected: false }))
      return
    }

    const channel = `product:${productId}`
    console.log(`Subscribing to real-time bidding for product: ${productId}`)

    setState(prev => ({ ...prev, isConnected: true, error: undefined }))

    // Subscribe to product updates
    subscribe(channel, handleBidUpdate)

    return () => {
      console.log(`Unsubscribing from real-time bidding for product: ${productId}`)
      unsubscribe(channel, handleBidUpdate)
    }
  }, [productId, isConnected, subscribe, unsubscribe, handleBidUpdate]) // Now safe to include handleBidUpdate

  // Manual refresh function
  const refreshBidData = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: ['products', 'detail', productId]
    })
    queryClient.invalidateQueries({
      queryKey: ['bidding', 'history', productId]
    })
  }, [productId, queryClient])

  return {
    ...state,
    refreshBidData,
    isRealtimeEnabled: isConnected
  }
}

// Hook for auction status updates
export const useAuctionStatus = (productId: string) => {
  const { isConnected, subscribe, unsubscribe } = useWebSocketContextRequired()
  const [auctionStatus, setAuctionStatus] = useState<{
    isActive: boolean
    endTime?: string
    isExtended?: boolean
    extensionCount?: number
  }>({
    isActive: true
  })

  const handleAuctionUpdate = useCallback((data: any) => {
    if (data.type === 'auction_extended') {
      setAuctionStatus(prev => ({
        ...prev,
        endTime: data.newEndTime,
        isExtended: true,
        extensionCount: data.extensionCount
      }))
    } else if (data.type === 'auction_ended') {
      setAuctionStatus(prev => ({
        ...prev,
        isActive: false
      }))
    }
  }, [])

  useEffect(() => {
    if (!productId || !isConnected) return

    const channel = `auction:${productId}`
    subscribe(channel, handleAuctionUpdate)

    return () => {
      unsubscribe(channel, handleAuctionUpdate)
    }
  }, [productId, isConnected, subscribe, unsubscribe, handleAuctionUpdate])

  return auctionStatus
}
